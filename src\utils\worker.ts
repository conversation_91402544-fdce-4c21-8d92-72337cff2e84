import SparkMD5 from 'spark-md5';

export function Worker() {
  // 处理分片
  function createChunks(file: File, index: number, CHUNK_SIZE: number): Promise<{
    start: number,
    end: number,
    index: number,
    hash: string
  }> {
    return new Promise((resolve, reject) => {
      const start = index * CHUNK_SIZE;
      const end = start + CHUNK_SIZE;
      const chunk = file.slice(start, end); // 左闭右开 [start, end)
      // 哈希处理: 唯一标识, 文件指纹
      const spark = new SparkMD5.ArrayBuffer();
      const fileReader = new FileReader();
      fileReader.readAsArrayBuffer(chunk); // 读取为字节流
      fileReader.onload = (e) => {
        spark.append(e.target?.result as ArrayBuffer);
        resolve({
          start,
          end,
          index,
          hash: spark.end()
        });
      }
    })
  }

  onmessage = async ({ data: { file, CHUNK_SIZE, startIndex, endIndex } }) => {
    const promises: Promise<{
      start: number,
      end: number,
      index: number,
      hash: string
    }>[] = [];
    for (let i = startIndex; i < endIndex; i++) {
      const chunk = createChunks(file, i, CHUNK_SIZE); // i 代表第几个分片
      promises.push(chunk);
    }
    const chunks = await Promise.all(promises);
    postMessage(chunks);
  }


}
