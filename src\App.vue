<script setup lang="ts">
import { ref } from 'vue';

// 上传文件
const handleUpload = async (event: Event) => {
  const fileList = (event.target as HTMLInputElement).files;
  if (!fileList) return;

  const file = fileList[0];
  console.time('cutFile');
  // 文件切片
  const chunks = await cutFile(file);
  console.timeEnd('cutFile');
  console.log(chunks);
}

// 文件切片: 利用 Web Worker 并行处理
const cutFile = (file: File): Promise<any> => {
  // 分片大小
  const CHUNK_SIZE = 1024 * 1024 * 3; // 3M
  const THREAD_NUM = navigator.hardwareConcurrency || 4; // 线程数
  return new Promise((resolve, reject) => {
    // 存放分片结果
    const result: Promise<{
      start: number,
      end: number,
      index: number,
      hash: string
    }>[] = [];
    let finishCount = 0; // 统计线程完成的数量
    const chunkCount = Math.ceil(file.size / CHUNK_SIZE); // 分片总数
    const workerChunks = Math.ceil(chunkCount / THREAD_NUM); // 每个线程处理的分片数

    for (let i = 0; i < THREAD_NUM; i++) {
      // 创建 Web Worker 线程
      const worker = new Worker('./utils/worker.ts', { type: 'module' });
      // 计算每个线程处理的分片范围
      const startIndex = i * workerChunks;
      const endIndex = Math.min(startIndex + workerChunks, chunkCount);
      // 发送任务给 Web Worker 线程 (相关于委托任务)
      worker.postMessage({
        file,
        CHUNK_SIZE, // 分片大小
        startIndex, // 起始分片索引
        endIndex,   // 结束分片索引
      });
      // 监听 Web Worker 线程返回的结果
      worker.onmessage = ({ data }) => {
        // 不能确定哪个线程先完成, 因此不可以简单的 push
        for (let i = startIndex; i < endIndex; i++) {
          result[i] = data[i - startIndex];
        }
        worker.terminate(); // 关闭线程
        finishCount++;
        if (finishCount === THREAD_NUM) {
          resolve(result);
        }
      };
    }
  });
}

// 提交文件
const handleSubmit = () => {
  // TODO: 提交文件
}
</script>

<template>
  <input type="file" @change="handleUpload" />
  <button @click="handleSubmit">提交</button>
</template>

<style scoped></style>
